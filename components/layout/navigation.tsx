'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Button } from '@/components/ui/button';
import { Scale, Menu, X } from 'lucide-react';

export function Navigation() {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isClient, setIsClient] = useState(false);
  const t = useTranslations('navigation');

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Render loading state if not client-side yet
  if (!isClient) {
    return (
      <nav className="bg-black/95 backdrop-blur-xl fixed w-full z-50 border-b border-yellow-500/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-yellow-500/10 rounded-xl border border-yellow-500/20">
                <Scale className="h-6 w-6 text-yellow-400" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">
                DeLawPR
              </span>
            </div>
          </div>
        </div>
      </nav>
    );
  }

  return (
    <nav className="bg-black/95 backdrop-blur-xl fixed w-full z-50 border-b border-yellow-500/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="p-2 bg-yellow-500/10 rounded-xl border border-yellow-500/20 group-hover:bg-yellow-500/20 transition-all duration-300">
              <Scale className="h-6 w-6 text-yellow-400" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">
              DeLawPR
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-1">
            <Link 
              href="#hero" 
              className="px-4 py-2 text-sm font-medium text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            >
              Inicio
            </Link>
            <Link 
              href="#services-showcase" 
              className="px-4 py-2 text-sm font-medium text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            >
              Servicios
            </Link>
            <Link 
              href="/es/lawyers" 
              className="px-4 py-2 text-sm font-medium text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            >
              Abogados
            </Link>
            <Link
              href="#testimonials-section"
              className="px-4 py-2 text-sm font-medium text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            >
              Testimonios
            </Link>
          </div>

          {/* Auth Buttons */}
          <div className="flex items-center space-x-3">
            <Link href="/es/login">
              <Button 
                variant="ghost" 
                className="hidden md:inline-flex px-4 py-2 text-sm font-medium text-white hover:text-yellow-400 hover:bg-yellow-500/10 border border-gray-700 hover:border-yellow-500/30 rounded-lg transition-all duration-200"
              >
                Iniciar Sesión
              </Button>
            </Link>
            <Link href="/es/register">
              <Button className="hidden md:inline-flex px-4 py-2 text-sm font-medium bg-gradient-to-r from-yellow-500 to-yellow-600 text-black hover:from-yellow-400 hover:to-yellow-500 rounded-lg transition-all duration-200 shadow-lg shadow-yellow-500/20">
                Registrarse
              </Button>
            </Link>
            
            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              className="md:hidden p-2 text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <div className={`md:hidden transition-all duration-300 ${isMobileMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="bg-black/95 backdrop-blur-xl border-t border-yellow-500/20 px-4 py-4 space-y-2">
          <Link 
            href="#hero" 
            className="block px-4 py-2 text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Inicio
          </Link>
          <Link 
            href="#services-showcase" 
            className="block px-4 py-2 text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Servicios
          </Link>
          <Link 
            href="/es/lawyers" 
            className="block px-4 py-2 text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Abogados
          </Link>
          <Link
            href="#testimonials-section"
            className="block px-4 py-2 text-white hover:text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200"
            onClick={() => setIsMobileMenuOpen(false)}
          >
            Testimonios
          </Link>
          
          <div className="pt-4 border-t border-gray-700 space-y-2">
            <Link href="/es/login" className="block">
              <Button 
                variant="ghost" 
                className="w-full justify-start px-4 py-2 text-white hover:text-yellow-400 hover:bg-yellow-500/10 border border-gray-700 hover:border-yellow-500/30 rounded-lg transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Iniciar Sesión
              </Button>
            </Link>
            <Link href="/es/register" className="block">
              <Button
                className="w-full justify-center px-4 py-2 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black hover:from-yellow-400 hover:to-yellow-500 rounded-lg transition-all duration-200"
                onClick={() => setIsMobileMenuOpen(false)}
              >
                Registrarse
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
}
