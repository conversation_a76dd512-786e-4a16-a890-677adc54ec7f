'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Scale, Mail, Phone, MapPin, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';

export default function Footer() {
  const pathname = usePathname() || '';
  const locale = pathname.split('/').filter(Boolean)[0] || 'es';

  const legalServices = [
    { name: 'Derecho de Familia', path: '/es/lawyers?category=family' },
    { name: 'Derecho Inmobiliario', path: '/es/lawyers?category=real-estate' },
    { name: 'Derecho Corporativo', path: '/es/lawyers?category=corporate' },
    { name: 'Derecho Laboral', path: '/es/lawyers?category=labor' }
  ];

  const supportLinks = [
    { name: 'Centro de Ayuda', path: '/es/soporte/centro-ayuda' },
    { name: 'Preguntas Frecuentes', path: '/es/soporte/preguntas-frecuentes' },
    { name: '<PERSON><PERSON><PERSON>', path: '/es/soporte/guias-legales' },
    { name: '<PERSON><PERSON>', path: '/es/soporte/contacto' }
  ];

  const companyLinks = [
    { name: 'Acerca de Nosotros', path: '/es/empresa/acerca-de-nosotros' },
    { name: 'Blog Legal', path: '/es/empresa/blog-legal' },
    { name: 'Carreras', path: '/es/empresa/carreras' },
    { name: 'Prensa', path: '/es/empresa/prensa' }
  ];

  const legalLinks = [
    { name: 'Términos de Servicio', path: '/es/legal/terminos' },
    { name: 'Política de Privacidad', path: '/es/legal/privacidad' },
    { name: 'Cookies', path: '/es/legal/cookies' },
    { name: 'Licencias', path: '/es/legal/licencias' }
  ];

  return (
    <footer className="relative bg-black border-t border-gray-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-3 mb-6">
              <div className="p-2 bg-yellow-500/10 rounded-xl border border-yellow-500/20">
                <Scale className="h-6 w-6 text-yellow-400" />
              </div>
              <span className="text-xl font-bold bg-gradient-to-r from-yellow-300 to-yellow-500 bg-clip-text text-transparent">
                DeLawPR
              </span>
            </Link>
            <p className="text-gray-400 mb-6 leading-relaxed">
              La plataforma legal más confiable de Puerto Rico. Conectamos personas con abogados expertos.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-sm text-gray-400">
                <MapPin className="h-4 w-4 text-yellow-400" />
                <span>San Juan, Puerto Rico</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-gray-400">
                <Phone className="h-4 w-4 text-yellow-400" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-3 text-sm text-gray-400">
                <Mail className="h-4 w-4 text-yellow-400" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Links Grid */}
          <div className="lg:col-span-3 grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Services */}
            <div>
              <h3 className="text-white font-semibold mb-4">Servicios Legales</h3>
              <ul className="space-y-3">
                {legalServices.map((item, index) => (
                  <li key={index}>
                    <Link 
                      href={item.path}
                      className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 text-sm"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support */}
            <div>
              <h3 className="text-white font-semibold mb-4">Soporte</h3>
              <ul className="space-y-3">
                {supportLinks.map((item, index) => (
                  <li key={index}>
                    <Link 
                      href={item.path}
                      className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 text-sm"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Company */}
            <div>
              <h3 className="text-white font-semibold mb-4">Empresa</h3>
              <ul className="space-y-3">
                {companyLinks.map((item, index) => (
                  <li key={index}>
                    <Link 
                      href={item.path}
                      className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 text-sm"
                    >
                      {item.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-gray-800">
          <div className="flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0">
            {/* Legal Links */}
            <div className="flex flex-wrap items-center space-x-6">
              {legalLinks.map((item, index) => (
                <Link 
                  key={index}
                  href={item.path}
                  className="text-gray-400 hover:text-yellow-400 transition-colors duration-200 text-sm"
                >
                  {item.name}
                </Link>
              ))}
            </div>

            {/* Social Media */}
            <div className="flex items-center space-x-4">
              <a 
                href="https://facebook.com/delawpr" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors duration-200"
              >
                <Facebook className="h-4 w-4 text-gray-400 hover:text-white" />
              </a>
              <a 
                href="https://twitter.com/delawpr" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors duration-200"
              >
                <Twitter className="h-4 w-4 text-gray-400 hover:text-white" />
              </a>
              <a 
                href="https://instagram.com/delawpr" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors duration-200"
              >
                <Instagram className="h-4 w-4 text-gray-400 hover:text-white" />
              </a>
              <a 
                href="https://linkedin.com/company/delawpr" 
                target="_blank" 
                rel="noopener noreferrer"
                className="p-2 bg-gray-800 hover:bg-gray-700 rounded-lg transition-colors duration-200"
              >
                <Linkedin className="h-4 w-4 text-gray-400 hover:text-white" />
              </a>
            </div>
          </div>

          {/* Copyright */}
          <div className="mt-8 text-center">
            <p className="text-gray-400 text-sm">
              © 2024 DeLawPR. Todos los derechos reservados. Hecho con ❤️ en Puerto Rico.
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
}
