'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';

import { Scale, Shield, Users, Award } from 'lucide-react';

export default function HeroProfessional() {
  const stats = [
    { label: 'Abogados Verificados', value: '500+', icon: Shield },
    { label: 'Casos Resueltos', value: '15,000+', icon: Scale },
    { label: 'Clientes Satisfechos', value: '12,000+', icon: Users },
    { label: 'Años de Experiencia', value: '25+', icon: Award },
  ];

  return (
    <section id="hero" className="relative min-h-screen flex items-center justify-center pt-16 pb-20">
      <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center space-y-8">
          {/* Main Heading */}
          <div className="space-y-4">
            <h1 className="text-5xl md:text-7xl font-bold text-white leading-tight">
              Encuentra el{' '}
              <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
                abogado perfecto
              </span>
              <br />
              para tu caso
            </h1>
            <p className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
              Conectamos personas con abogados expertos en Puerto Rico.
              <span className="block text-yellow-400 font-semibold mt-2">
                Rápido • Seguro • Profesional
              </span>
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4 pt-4">
            <Link href="/es/lawyers">
              <Button className="px-8 py-4 text-lg font-semibold bg-gradient-to-r from-yellow-500 to-yellow-600 text-black hover:from-yellow-400 hover:to-yellow-500 rounded-xl transition-all duration-300 shadow-2xl shadow-yellow-500/25 hover:shadow-yellow-500/40 transform hover:scale-105">
                Buscar Abogado Ahora
              </Button>
            </Link>
            <Link href="#services-showcase">
              <Button variant="outline" className="px-8 py-4 text-lg font-semibold border-2 border-yellow-400 text-yellow-400 bg-transparent hover:bg-yellow-500/10 hover:border-yellow-300 rounded-xl transition-all duration-300">
                Ver Servicios
              </Button>
            </Link>
          </div>

          {/* Trust Indicators */}
          <div className="pt-12">
            <p className="text-sm text-gray-400 mb-6">Confiado por miles de puertorriqueños</p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="text-center group">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-yellow-500/10 rounded-xl border border-yellow-500/20 group-hover:bg-yellow-500/20 transition-all duration-300 mb-3">
                    <stat.icon className="h-6 w-6 text-yellow-400" />
                  </div>
                  <div className="text-2xl md:text-3xl font-bold text-white mb-1">{stat.value}</div>
                  <div className="text-sm text-gray-400">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
