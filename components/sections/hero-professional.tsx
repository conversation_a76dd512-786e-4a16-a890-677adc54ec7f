'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Scale, Shield, Users, Award } from 'lucide-react';

export default function HeroProfessional() {
  const stats = [
    { label: 'Abogados Verificados', value: '500+', icon: Shield },
    { label: 'Casos Resueltos', value: '15,000+', icon: Scale },
    { label: 'Clientes Satisfechos', value: '12,000+', icon: Users },
    { label: 'Años de Experiencia', value: '25+', icon: Award },
  ];

  return (
    <section id="hero" className="relative overflow-hidden pt-32 pb-24 md:pt-40 md:pb-32">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Hero Content */}
        <div className="text-center">
          <h1 className="text-4xl tracking-tight font-extrabold text-white sm:text-5xl md:text-6xl">
            <span className="block xl:inline">Encuentra al abogado</span>{' '}
            <span className="block bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-400 bg-clip-text text-transparent">
              perfecto para tu caso
            </span>
          </h1>
          <p className="mt-3 max-w-md mx-auto text-base text-gray-300 sm:text-lg md:mt-5 md:text-xl md:max-w-3xl">
            Conectamos a personas con abogados expertos en todas las áreas del derecho.
            <span className="block text-yellow-300 font-medium mt-2">Rápido, seguro y sin complicaciones.</span>
          </p>
          <div className="mt-8 flex flex-col sm:flex-row justify-center gap-4">
            <Link href="/es/lawyers" passHref>
              <Button className="px-8 py-3 border border-transparent text-base font-medium rounded-lg text-gray-900 bg-gradient-to-r from-yellow-500 via-yellow-400 to-yellow-600 hover:from-yellow-400 hover:via-yellow-300 hover:to-yellow-500 md:py-4 md:text-lg md:px-10 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-yellow-500/30">
                Buscar Abogado
              </Button>
            </Link>
            <Link href="#services-showcase" passHref>
              <Button variant="outline" className="px-8 py-3 border-2 border-yellow-400 text-base font-medium rounded-lg text-yellow-400 bg-transparent hover:bg-yellow-500/10 md:py-4 md:text-lg md:px-10 transition-all duration-200 transform hover:scale-105 hover:border-yellow-300">
                Más Información
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Section */}
        <div className="mt-20 grid grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => (
            <Card key={index} className="text-center p-6 bg-white/5 backdrop-blur-sm border border-white/10 hover:border-yellow-500/30 transition-colors">
              <CardContent className="p-0">
                <div className="w-16 h-16 bg-yellow-500/10 rounded-full flex items-center justify-center mx-auto mb-4 border border-yellow-500/20">
                  <stat.icon className="h-6 w-6 text-yellow-400" />
                </div>
                <div className="text-3xl font-bold text-yellow-400 mb-2">{stat.value}</div>
                <div className="text-sm text-gray-300">{stat.label}</div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
}
