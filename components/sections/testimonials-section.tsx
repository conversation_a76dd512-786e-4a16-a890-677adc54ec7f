'use client';

import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Star, Quote, Verified, Users, Award, TrendingUp } from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Empresaria',
    rating: 5,
    comment: 'Excelente servicio. Mi abogado me ayudó con mi divorcio de manera profesional y compasiva. Altamente recomendado.',
    lawyerName: '<PERSON><PERSON><PERSON>',
    specialty: 'Derecho de Familia',
    avatar: '<PERSON>'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Propietario',
    rating: 5,
    comment: 'Proceso de compra de casa muy fluido. El abogado explicó todo claramente y resolvió todos mis problemas.',
    lawyerName: 'Lcdo. <PERSON>',
    specialty: 'Derecho Inmobiliario',
    avatar: 'CM'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Directora de RRHH',
    rating: 5,
    comment: 'Increíble asesoría legal para mi empresa. Profesional, rápido y muy conocedor de las leyes laborales.',
    lawyerName: 'Lcda. Isabel Morales',
    specialty: 'Derecho Laboral',
    avatar: 'AR'
  }
];

const stats = [
  {
    icon: Users,
    value: '12,000+',
    label: 'Clientes Satisfechos'
  },
  {
    icon: Award,
    value: '98.7%',
    label: 'Tasa de Éxito'
  },
  {
    icon: TrendingUp,
    value: '4.9/5',
    label: 'Calificación Promedio'
  },
  {
    icon: Verified,
    value: '500+',
    label: 'Abogados Verificados'
  }
];

export function TestimonialsSection() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-600'}`}
      />
    ));
  };

  return (
    <section id="testimonials-section" className="relative py-20 overflow-hidden">
      <div className="absolute inset-0 bg-black"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-transparent border border-yellow-400/30 rounded-full mb-6">
            <Quote className="w-5 h-5 text-yellow-400 mr-2" />
            <span className="text-yellow-400 font-medium">Testimonios</span>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Lo que Dicen{' '}
            <span className="text-yellow-400">
              Nuestros Clientes
            </span>
          </h2>
          <p className="text-xl text-white max-w-3xl mx-auto">
            Miles de puertorriqueños han encontrado la justicia que merecían a través de nuestra plataforma.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center group">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-transparent border border-yellow-400/30 rounded-2xl group-hover:border-yellow-400 transition-all duration-300 mb-4">
                <stat.icon className="h-8 w-8 text-yellow-400" />
              </div>
              <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
              <div className="text-sm text-white/60">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Testimonials */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <Card
              key={testimonial.id}
              className="bg-transparent border border-white/20 hover:border-yellow-400 transition-all duration-300 group"
            >
              <CardContent className="p-6">
                {/* Quote Icon */}
                <div className="flex justify-between items-start mb-4">
                  <Quote className="h-8 w-8 text-yellow-400/60" />
                  <div className="flex items-center space-x-1">
                    {renderStars(testimonial.rating)}
                  </div>
                </div>

                {/* Comment */}
                <p className="text-white/80 mb-6 leading-relaxed">
                  "{testimonial.comment}"
                </p>

                {/* Client Info */}
                <div className="flex items-center space-x-3 mb-4">
                  <Avatar className="h-12 w-12 border-2 border-yellow-400/30">
                    <AvatarFallback className="bg-transparent border border-yellow-400/30 text-yellow-400 font-semibold">
                      {testimonial.avatar}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="flex items-center space-x-2">
                      <h4 className="font-semibold text-white">{testimonial.name}</h4>
                      <Verified className="h-4 w-4 text-yellow-400" />
                    </div>
                    <p className="text-sm text-white/60">{testimonial.role}</p>
                  </div>
                </div>

                {/* Lawyer Info */}
                <div className="pt-4 border-t border-white/20">
                  <p className="text-sm text-white/60">
                    Representado por{' '}
                    <span className="text-yellow-400 font-medium">{testimonial.lawyerName}</span>
                  </p>
                  <p className="text-xs text-white/40">{testimonial.specialty}</p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="bg-transparent border border-yellow-400/30 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Únete a Miles de Clientes Satisfechos
            </h3>
            <p className="text-white/80 mb-6">
              Encuentra tu abogado ideal hoy y experimenta el mismo nivel de excelencia que nuestros clientes.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <div className="flex items-center space-x-2 text-sm text-white/60">
                <Verified className="h-4 w-4 text-yellow-400" />
                <span>Consulta gratuita</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-white/60">
                <Verified className="h-4 w-4 text-yellow-400" />
                <span>Sin compromiso</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-white/60">
                <Verified className="h-4 w-4 text-yellow-400" />
                <span>Respuesta en 24h</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
