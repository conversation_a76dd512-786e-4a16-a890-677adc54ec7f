'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { 
  Scale, 
  Shield, 
  Users, 
  Home, 
  Building, 
  Heart, 
  FileText, 
  DollarSign,
  Briefcase,
  Award,
  Clock,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const legalCategories = [
  {
    id: 'family',
    title: 'Derecho de Familia',
    icon: Heart,
    description: 'Protegemos lo que más importa: tu familia',
    color: 'bg-yellow-600/20',
    lightColor: 'bg-yellow-500/10',
    textColor: 'text-yellow-400',
    cases: '2,847',
    successRate: '94%',
    avgCost: '$1,200 - $3,500',
    timeframe: '2-6 meses',
    services: [
      'Divorcio y separación legal',
      'Custodia y patria potestad',
      'Pensión alimentaria',
      'Adopción y tutela',
      'Violencia doméstica',
      'Acuerdos prematrimoniales'
    ],
    lawyers: 89,
    featured: true
  },
  {
    id: 'real-estate',
    title: 'Derecho Inmobiliario',
    icon: Home,
    description: 'Tu patrimonio inmobiliario en manos expertas',
    color: 'bg-amber-600/20',
    lightColor: 'bg-amber-500/10',
    textColor: 'text-amber-400',
    cases: '1,923',
    successRate: '97%',
    avgCost: '$800 - $2,500',
    timeframe: '1-3 meses',
    services: [
      'Compra y venta de propiedades',
      'Títulos de propiedad',
      'Disputas de linderos',
      'Contratos de arrendamiento',
      'Zonificación y permisos',
      'Ejecuciones hipotecarias'
    ],
    lawyers: 67,
    featured: true
  },
  {
    id: 'criminal',
    title: 'Derecho Penal',
    icon: Shield,
    description: 'Defensa sólida para proteger tus derechos',
    color: 'bg-yellow-700/20',
    lightColor: 'bg-yellow-600/10',
    textColor: 'text-yellow-300',
    cases: '3,156',
    successRate: '91%',
    avgCost: '$2,000 - $8,000',
    timeframe: '3-12 meses',
    services: [
      'Defensa criminal',
      'Delitos federales',
      'Violaciones de tránsito',
      'Delitos de drogas',
      'Violencia doméstica',
      'Apelaciones penales'
    ],
    lawyers: 45,
    featured: true
  },
  {
    id: 'employment',
    title: 'Derecho Laboral',
    icon: Users,
    description: 'Protegemos tus derechos como trabajador',
    color: 'bg-amber-700/20',
    lightColor: 'bg-amber-600/10',
    textColor: 'text-amber-300',
    cases: '1,567',
    successRate: '89%',
    avgCost: '$1,500 - $4,000',
    timeframe: '2-8 meses',
    services: [
      'Despidos injustificados',
      'Discriminación laboral',
      'Acoso en el trabajo',
      'Salarios no pagados',
      'Compensación por accidentes',
      'Contratos laborales'
    ],
    lawyers: 52,
    featured: false
  },
  {
    id: 'corporate',
    title: 'Derecho Corporativo',
    icon: Building,
    description: 'Soluciones legales para tu empresa',
    color: 'bg-yellow-800/20',
    lightColor: 'bg-yellow-700/10',
    textColor: 'text-yellow-200',
    cases: '892',
    successRate: '96%',
    avgCost: '$2,500 - $10,000',
    timeframe: '1-6 meses',
    services: [
      'Formación de empresas',
      'Contratos comerciales',
      'Fusiones y adquisiciones',
      'Propiedad intelectual',
      'Cumplimiento regulatorio',
      'Disputas comerciales'
    ],
    lawyers: 34,
    featured: false
  },
  {
    id: 'personal-injury',
    title: 'Lesiones Personales',
    icon: Briefcase,
    description: 'Compensación justa por tus lesiones',
    color: 'bg-amber-800/20',
    lightColor: 'bg-amber-700/10',
    textColor: 'text-orange-600',
    cases: '2,234',
    successRate: '93%',
    avgCost: 'Sin costo inicial',
    timeframe: '6-18 meses',
    services: [
      'Accidentes automovilísticos',
      'Negligencia médica',
      'Accidentes de trabajo',
      'Productos defectuosos',
      'Caídas y resbalones',
      'Muerte por negligencia'
    ],
    lawyers: 41,
    featured: false
  }
];

const processSteps = [
  {
    step: '01',
    title: 'Consulta Inicial',
    description: 'Describe tu caso y recibe asesoría gratuita de 30 minutos',
    icon: FileText
  },
  {
    step: '02',
    title: 'Encuentra tu Abogado',
    description: 'Te conectamos con abogados especializados en tu área',
    icon: Users
  },
  {
    step: '03',
    title: 'Programa tu Cita',
    description: 'Agenda una consulta en persona o virtual',
    icon: Clock
  },
  {
    step: '04',
    title: 'Resuelve tu Caso',
    description: 'Tu abogado trabaja para obtener el mejor resultado',
    icon: Award
  }
];

export function ServicesShowcase() {
  const [activeTab, setActiveTab] = useState('all');
  const [expandedCard, setExpandedCard] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const filteredCategories = activeTab === 'all' 
    ? legalCategories 
    : legalCategories.filter(category => 
        activeTab === 'popular' ? category.featured : true
      );

  const toggleCard = (id: string) => {
    setExpandedCard(expandedCard === id ? null : id);
  };

  return (
    <section id="services-showcase" className="py-20 relative overflow-hidden">

      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge className="mb-4 px-6 py-3 bg-yellow-500/10 backdrop-blur-xl text-yellow-400 border border-yellow-500/30 shadow-2xl">
            <Scale className="h-4 w-4 mr-2" />
            Servicios Legales Especializados
          </Badge>
          <h2 className="text-4xl lg:text-6xl font-bold text-white mb-6">
            Expertos en Todas las
            <span className="bg-gradient-to-r from-yellow-300 via-yellow-200 to-yellow-400 bg-clip-text text-transparent block">
              Áreas del Derecho
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
            Más de 500 abogados especializados listos para ayudarte.
            Encuentra el experto perfecto para tu caso específico.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {filteredCategories.map((category) => (
            <Card
              key={category.id}
              className={`group cursor-pointer transition-all duration-500 transform hover:scale-105 ${
                selectedCategory === category.id
                  ? 'ring-2 ring-yellow-400 shadow-2xl bg-gray-900/80 backdrop-blur-xl border border-yellow-400/50'
                  : 'hover:shadow-2xl bg-gray-900/40 backdrop-blur-xl border border-gray-800/50 hover:border-yellow-500/50 hover:bg-gray-900/60'
              }`}
              onClick={() => setSelectedCategory(category.id)}
            >
              <CardHeader className="pb-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-12 h-12 bg-yellow-500/10 rounded-xl flex items-center justify-center border border-yellow-500/20">
                    <category.icon className="h-6 w-6 text-yellow-400" />
                  </div>
                  {category.featured && (
                    <Badge className="bg-yellow-500/20 text-yellow-300 border border-yellow-500/30 text-xs">
                      Popular
                    </Badge>
                  )}
                </div>
                <CardTitle className="text-xl font-bold text-white mb-2">
                  {category.title}
                </CardTitle>
                <p className="text-gray-300 text-sm leading-relaxed">
                  {category.description}
                </p>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="text-center p-2 bg-gray-800/50 rounded-lg border border-gray-700/50">
                    <div className="text-2xl font-bold text-yellow-400">{category.cases}</div>
                    <div className="text-xs text-gray-300">Casos</div>
                  </div>
                  <div className="text-center p-2 bg-gray-800/50 rounded-lg border border-gray-700/50">
                    <div className="text-2xl font-bold text-yellow-400">{category.successRate}</div>
                    <div className="text-xs text-gray-300">Éxito</div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-700/50">
                      <p className="text-sm font-medium text-gray-400">Casos</p>
                      <p className="text-lg font-semibold text-white">{category.cases}</p>
                    </div>
                    <div className="p-3 rounded-lg bg-gray-800/50 border border-gray-700/50">
                      <p className="text-sm font-medium text-gray-400">Tasa de éxito</p>
                      <p className="text-lg font-semibold text-yellow-400">{category.successRate}</p>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-700">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-gray-400">Costo promedio</span>
                      <span className="text-sm font-medium text-white">{category.avgCost}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-400">Tiempo promedio</span>
                      <span className="text-sm font-medium text-white">{category.timeframe}</span>
                    </div>
                  </div>
                  <div className="pt-4 border-t border-gray-700">
                    <h4 className="text-sm font-semibold text-yellow-400 mb-3">Servicios comunes:</h4>
                    <ul className="space-y-2">
                      {category.services.slice(0, 3).map((service, i) => (
                        <li key={i} className="flex items-start">
                          <CheckCircle className="h-5 w-5 text-yellow-500/80 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-gray-200">{service}</span>
                        </li>
                      ))}
                    </ul>
                    {category.services.length > 3 && (
                      <button 
                        onClick={() => toggleCard(category.id)}
                        className="mt-2 text-sm font-medium text-yellow-400 hover:text-yellow-300 flex items-center group"
                      >
                        Ver {category.services.length - 3} más
                        <ArrowRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform" />
                      </button>
                    )}
                  </div>

                  <div className="pt-4 border-t border-gray-700">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <Users className="h-5 w-5 text-yellow-500/80 mr-2" />
                        <span className="text-sm text-gray-300">{category.lawyers} abogados</span>
                      </div>
                      <Link href="/es/lawyers" passHref>
                        <Button 
                          variant="outline" 
                          className="text-sm font-medium bg-transparent border-yellow-500/30 text-yellow-400 hover:bg-yellow-500/10 hover:border-yellow-400/50 hover:text-yellow-300 group"
                        >
                          Ver abogados
                          <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
                        </Button>
                      </Link>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Link href="/es/lawyers" passHref>
            <Button
              className="px-8 py-3 text-base font-medium rounded-lg bg-gradient-to-r from-yellow-500 via-yellow-400 to-yellow-600 text-gray-900 hover:from-yellow-400 hover:via-yellow-300 hover:to-yellow-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-yellow-500/30"
            >
              Ver todos los servicios
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
