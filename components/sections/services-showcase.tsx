'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Scale, 
  Heart, 
  Home, 
  Building, 
  Briefcase,
  FileText,
  Users,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const legalServices = [
  {
    id: 'family',
    title: 'Derecho de Familia',
    icon: Heart,
    description: 'Di<PERSON><PERSON><PERSON>, custodia, pensión alimentaria y más',
    lawyers: 89,
    cases: '2,847+',
    color: 'from-pink-500/20 to-red-500/20',
    borderColor: 'border-pink-500/30',
    iconColor: 'text-pink-400'
  },
  {
    id: 'real-estate',
    title: 'Derecho Inmobiliario',
    icon: Home,
    description: 'Compra, venta, contratos y disputas de propiedad',
    lawyers: 67,
    cases: '1,923+',
    color: 'from-blue-500/20 to-cyan-500/20',
    borderColor: 'border-blue-500/30',
    iconColor: 'text-blue-400'
  },
  {
    id: 'corporate',
    title: 'Derecho Corporativo',
    icon: Building,
    description: 'Constitución de empresas, contratos comerciales',
    lawyers: 54,
    cases: '1,456+',
    color: 'from-green-500/20 to-emerald-500/20',
    borderColor: 'border-green-500/30',
    iconColor: 'text-green-400'
  },
  {
    id: 'labor',
    title: 'Derecho Laboral',
    icon: Briefcase,
    description: 'Despidos, discriminación, accidentes laborales',
    lawyers: 43,
    cases: '987+',
    color: 'from-purple-500/20 to-violet-500/20',
    borderColor: 'border-purple-500/30',
    iconColor: 'text-purple-400'
  },
  {
    id: 'civil',
    title: 'Derecho Civil',
    icon: FileText,
    description: 'Contratos, responsabilidad civil, daños',
    lawyers: 76,
    cases: '2,134+',
    color: 'from-orange-500/20 to-amber-500/20',
    borderColor: 'border-orange-500/30',
    iconColor: 'text-orange-400'
  },
  {
    id: 'criminal',
    title: 'Derecho Penal',
    icon: Scale,
    description: 'Defensa criminal, delitos, apelaciones',
    lawyers: 38,
    cases: '756+',
    color: 'from-red-500/20 to-rose-500/20',
    borderColor: 'border-red-500/30',
    iconColor: 'text-red-400'
  }
];

export function ServicesShowcase() {
  const [hoveredService, setHoveredService] = useState<string | null>(null);

  return (
    <section id="services-showcase" className="relative py-20 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-b from-gray-900 to-black"></div>
      
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-yellow-500/10 border border-yellow-500/20 rounded-full mb-6">
            <Scale className="w-5 h-5 text-yellow-400 mr-2" />
            <span className="text-yellow-400 font-medium">Servicios Legales</span>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Expertos en{' '}
            <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
              Todas las Áreas
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Conectamos con abogados certificados especializados en las principales áreas del derecho en Puerto Rico.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {legalServices.map((service) => (
            <Card 
              key={service.id}
              className={`group relative overflow-hidden bg-gradient-to-br ${service.color} backdrop-blur-sm border ${service.borderColor} hover:border-yellow-500/50 transition-all duration-300 cursor-pointer transform hover:scale-105`}
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className={`p-3 bg-black/20 rounded-xl border border-white/10`}>
                    <service.icon className={`h-6 w-6 ${service.iconColor}`} />
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-400">Abogados</div>
                    <div className="text-lg font-bold text-white">{service.lawyers}</div>
                  </div>
                </div>
                
                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-yellow-400 transition-colors">
                  {service.title}
                </h3>
                <p className="text-gray-300 text-sm mb-4 leading-relaxed">
                  {service.description}
                </p>
                
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="h-4 w-4 text-green-400" />
                      <span className="text-xs text-gray-400">{service.cases} casos</span>
                    </div>
                  </div>
                  <ArrowRight className={`h-4 w-4 text-gray-400 group-hover:text-yellow-400 transition-all duration-300 ${hoveredService === service.id ? 'translate-x-1' : ''}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-yellow-500/10 to-yellow-600/10 border border-yellow-500/20 rounded-2xl p-8 mb-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              ¿No encuentras tu área legal?
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Tenemos abogados especializados en más de 20 áreas del derecho. Encuentra el experto perfecto para tu caso específico.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link href="/es/lawyers">
                <Button className="px-8 py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 text-black hover:from-yellow-400 hover:to-yellow-500 rounded-lg transition-all duration-200 shadow-lg shadow-yellow-500/25">
                  Ver Todos los Abogados
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/es/soporte/contacto">
                <Button variant="outline" className="px-8 py-3 border-yellow-400 text-yellow-400 hover:bg-yellow-500/10 rounded-lg transition-all duration-200">
                  Consulta Gratuita
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
