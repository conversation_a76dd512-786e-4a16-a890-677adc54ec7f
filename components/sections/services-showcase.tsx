'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Scale, 
  Heart, 
  Home, 
  Building, 
  Briefcase,
  FileText,
  Users,
  ArrowRight,
  CheckCircle
} from 'lucide-react';

const legalServices = [
  {
    id: 'family',
    title: 'Derecho de Familia',
    icon: Heart,
    description: 'Di<PERSON><PERSON><PERSON>, custodia, pensión alimentaria y más',
    lawyers: 89,
    cases: '2,847+'
  },
  {
    id: 'real-estate',
    title: 'Derecho Inmobiliario',
    icon: Home,
    description: 'Compra, venta, contratos y disputas de propiedad',
    lawyers: 67,
    cases: '1,923+'
  },
  {
    id: 'corporate',
    title: 'Derecho Corporativo',
    icon: Building,
    description: 'Constitución de empresas, contratos comerciales',
    lawyers: 54,
    cases: '1,456+'
  },
  {
    id: 'labor',
    title: 'Derecho Laboral',
    icon: Briefcase,
    description: 'Despidos, discriminación, accidentes laborales',
    lawyers: 43,
    cases: '987+'
  },
  {
    id: 'civil',
    title: 'Derecho Civil',
    icon: FileText,
    description: 'Contratos, responsabilidad civil, daños',
    lawyers: 76,
    cases: '2,134+'
  },
  {
    id: 'criminal',
    title: 'Derecho Penal',
    icon: Scale,
    description: 'Defensa criminal, delitos, apelaciones',
    lawyers: 38,
    cases: '756+'
  }
];

export function ServicesShowcase() {
  const [hoveredService, setHoveredService] = useState<string | null>(null);

  return (
    <section id="services-showcase" className="relative py-20 overflow-hidden">
      <div className="absolute inset-0 bg-black"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-transparent border border-yellow-400/30 rounded-full mb-6">
            <Scale className="w-5 h-5 text-yellow-400 mr-2" />
            <span className="text-yellow-400 font-medium">Servicios Legales</span>
          </div>
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Expertos en{' '}
            <span className="text-yellow-400">
              Todas las Áreas
            </span>
          </h2>
          <p className="text-xl text-white max-w-3xl mx-auto">
            Conectamos con abogados certificados especializados en las principales áreas del derecho en Puerto Rico.
          </p>
        </div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {legalServices.map((service) => (
            <Card
              key={service.id}
              className="group relative overflow-hidden bg-transparent border border-white/20 hover:border-yellow-400 transition-all duration-300 cursor-pointer"
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="p-3 bg-transparent border border-yellow-400/30 rounded-xl">
                    <service.icon className="h-6 w-6 text-yellow-400" />
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-white/60">Abogados</div>
                    <div className="text-lg font-bold text-white">{service.lawyers}</div>
                  </div>
                </div>

                <h3 className="text-xl font-bold text-white mb-2 group-hover:text-yellow-400 transition-colors">
                  {service.title}
                </h3>
                <p className="text-white/80 text-sm mb-4 leading-relaxed">
                  {service.description}
                </p>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="h-4 w-4 text-yellow-400" />
                      <span className="text-xs text-white/60">{service.cases} casos</span>
                    </div>
                  </div>
                  <ArrowRight className={`h-4 w-4 text-white/60 group-hover:text-yellow-400 transition-all duration-300 ${hoveredService === service.id ? 'translate-x-1' : ''}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-transparent border border-yellow-400/30 rounded-2xl p-8 mb-8">
            <h3 className="text-2xl font-bold text-white mb-4">
              ¿No encuentras tu área legal?
            </h3>
            <p className="text-white/80 mb-6 max-w-2xl mx-auto">
              Tenemos abogados especializados en más de 20 áreas del derecho. Encuentra el experto perfecto para tu caso específico.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link href="/es/lawyers">
                <Button className="px-8 py-3 bg-yellow-400 text-black hover:bg-yellow-500 rounded-lg transition-all duration-200">
                  Ver Todos los Abogados
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/es/soporte/contacto">
                <Button variant="outline" className="px-8 py-3 border-yellow-400 text-yellow-400 hover:bg-yellow-400/10 bg-transparent rounded-lg transition-all duration-200">
                  Consulta Gratuita
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
