'use client';

import React from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Users, Scale } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function CTASection() {
  const t = useTranslations('cta');

  return (
    <section className="py-20 relative overflow-hidden">

      <div className="relative max-w-7xl mx-auto py-16 px-4 sm:px-6 lg:py-20 lg:px-8 lg:flex lg:items-center lg:justify-between">
        <h2 className="text-3xl lg:text-5xl font-bold tracking-tight text-white sm:text-4xl">
          <span className="block">{t('title')}</span>
          <span className="block bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 bg-clip-text text-transparent animate-pulse-slow">{t('subtitle')}</span>
        </h2>
        <div className="mt-8 flex flex-col sm:flex-row gap-4 lg:mt-0 lg:flex-shrink-0">
          <Link href="/es/lawyers" passHref>
            <Button size="lg" className="bg-gradient-to-r from-yellow-400 via-orange-500 to-yellow-400 hover:from-yellow-500 hover:via-orange-600 hover:to-yellow-500 text-black font-bold px-8 py-4 rounded-2xl shadow-2xl hover:shadow-yellow-500/25 transition-all duration-300 hover:scale-105 group">
              <Users className="mr-2 h-5 w-5" />
              <span className="relative z-10">Buscar Abogado</span>
              <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </Button>
          </Link>
          <Link href="/es/register" passHref>
            <Button size="lg" className="bg-white/10 backdrop-blur-xl border-2 border-white/20 text-white hover:bg-white/20 hover:border-white/30 px-8 py-4 rounded-2xl shadow-2xl transition-all duration-300 hover:scale-105">
              <Scale className="mr-2 h-5 w-5" />
              Únete como Abogado
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}
