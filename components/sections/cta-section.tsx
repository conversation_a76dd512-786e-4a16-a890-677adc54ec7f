'use client';

import React from 'react';
import Link from 'next/link';

import { Users, Scale } from 'lucide-react';
import { Button } from '@/components/ui/button';

export function CTASection() {
  return (
    <section className="relative py-20 overflow-hidden">
      <div className="absolute inset-0 bg-gradient-to-r from-gray-900 via-black to-gray-900"></div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-white mb-6">
            ¿Listo para Encontrar{' '}
            <span className="bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
              tu Abogado Ideal?
            </span>
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto mb-10">
            Únete a miles de puertorriqueños que han encontrado la justicia que merecían.
          </p>

          <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
            <Link href="/es/lawyers">
              <Button className="px-8 py-4 text-lg font-semibold bg-gradient-to-r from-yellow-500 to-yellow-600 text-black hover:from-yellow-400 hover:to-yellow-500 rounded-xl transition-all duration-300 shadow-2xl shadow-yellow-500/25 hover:shadow-yellow-500/40 transform hover:scale-105">
                <Users className="mr-2 h-5 w-5" />
                Buscar Abogado Ahora
              </Button>
            </Link>
            <Link href="/es/register">
              <Button variant="outline" className="px-8 py-4 text-lg font-semibold border-2 border-yellow-400 text-yellow-400 bg-transparent hover:bg-yellow-500/10 hover:border-yellow-300 rounded-xl transition-all duration-300">
                <Scale className="mr-2 h-5 w-5" />
                Únete como Abogado
              </Button>
            </Link>
          </div>

          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-400 mb-2">24/7</div>
              <div className="text-gray-400">Soporte Disponible</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-400 mb-2">100%</div>
              <div className="text-gray-400">Consulta Gratuita</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-400 mb-2">500+</div>
              <div className="text-gray-400">Abogados Verificados</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
