'use client';

import React, { useState, useEffect } from 'react';
import { Navigation } from '@/components/layout/navigation';
import UnifiedBackground from '@/components/layout/unified-background';
import Footer from '@/components/layout/footer';
import { Calendar, Clock, Search } from 'lucide-react';

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  date: string;
  readTime: string;
  category: string;
  image: string;
  author: {
    name: string;
    role: string;
    avatar: string;
  };
}

export default function BlogLegalClient() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('Todos');
  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Implement search functionality
    console.log('Searching for:', searchTerm);
  };
  
  const featuredPost: BlogPost = {
    id: 1,
    title: "Nuevas Reformas Laborales en Puerto Rico: Lo que Debes Saber",
    excerpt: "Un análisis detallado de los cambios recientes en la legislación laboral y cómo afectan a empleadores y empleados en la isla.",
    date: "15 de Enero, 2024",
    readTime: "5 min de lectura",
    category: "Derecho Laboral",
    image: "/images/blog/featured-post.jpg",
    author: {
      name: "Dra. María González",
      role: "Especialista en Derecho Laboral",
      avatar: "/images/team/maria-gonzalez.jpg"
    }
  };

  const blogPosts: BlogPost[] = [
    {
      id: 2,
      title: "Guía Completa sobre el Proceso de Divorcio en Puerto Rico",
      excerpt: "Todo lo que necesitas saber sobre los requisitos, plazos y consideraciones legales para un divorcio en la isla.",
      date: "12 de Enero, 2024",
      readTime: "7 min de lectura",
      category: "Derecho de Familia",
      image: "/images/blog/divorce-guide.jpg",
      author: {
        name: "Lcdo. Carlos Rivera",
        role: "Especialista en Derecho de Familia",
        avatar: "/images/team/carlos-rivera.jpg"
      }
    },
    {
      id: 3,
      title: "Cómo Proteger tu Negocio: Aspectos Legales Esenciales",
      excerpt: "Los fundamentos legales que todo empresario debe conocer para proteger su negocio en Puerto Rico.",
      date: "10 de Enero, 2024",
      readTime: "6 min de lectura",
      category: "Derecho Corporativo",
      image: "/images/blog/business-protection.jpg",
      author: {
        name: "Lcda. Ana Martínez",
        role: "Especialista en Derecho Corporativo",
        avatar: "/images/team/ana-martinez.jpg"
      }
    },
    {
      id: 4,
      title: "Derechos del Consumidor: Conoce tus Protecciones",
      excerpt: "Una guía completa sobre los derechos que tienes como consumidor y cómo ejercerlos efectivamente.",
      date: "8 de Enero, 2024",
      readTime: "4 min de lectura",
      category: "Derecho del Consumidor",
      image: "/images/blog/consumer-rights.jpg",
      author: {
        name: "Lcdo. Roberto Sánchez",
        role: "Especialista en Derecho del Consumidor",
        avatar: "/images/team/roberto-sanchez.jpg"
      }
    },
    {
      id: 5,
      title: "Planificación Patrimonial: Asegura el Futuro de tu Familia",
      excerpt: "Estrategias legales para proteger y transferir tu patrimonio de manera eficiente y segura.",
      date: "5 de Enero, 2024",
      readTime: "8 min de lectura",
      category: "Derecho Patrimonial",
      image: "/images/blog/estate-planning.jpg",
      author: {
        name: "Dra. Isabel Torres",
        role: "Especialista en Derecho Patrimonial",
        avatar: "/images/team/isabel-torres.jpg"
      }
    },
    {
      id: 6,
      title: "Inmigración a Puerto Rico: Proceso y Requisitos",
      excerpt: "Todo lo que necesitas saber sobre los procesos de inmigración y los requisitos legales en Puerto Rico.",
      date: "3 de Enero, 2024",
      readTime: "6 min de lectura",
      category: "Derecho de Inmigración",
      image: "/images/blog/immigration-guide.jpg",
      author: {
        name: "Lcdo. Miguel Rodríguez",
        role: "Especialista en Derecho de Inmigración",
        avatar: "/images/team/miguel-rodriguez.jpg"
      }
    }
  ];

  const categories = [
    "Todos",
    "Derecho Laboral",
    "Derecho de Familia",
    "Derecho Corporativo",
    "Derecho del Consumidor",
    "Derecho Patrimonial",
    "Derecho de Inmigración"
  ];

  const filteredPosts = selectedCategory === 'Todos' 
    ? blogPosts 
    : blogPosts.filter(post => post.category === selectedCategory);

  return (
    <div className="min-h-screen bg-black text-white">
      <Navigation />
      <UnifiedBackground />
      
      <div className="relative z-10 pt-20">
        {/* Header */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-white via-yellow-100 to-white bg-clip-text text-transparent">
              Blog Legal
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Mantente informado con los últimos desarrollos legales, análisis de casos y consejos prácticos de nuestros expertos en derecho puertorriqueño.
            </p>
          </div>

          {/* Featured Post */}
          <div className="mb-16">
            <div className="bg-gradient-to-r from-gray-900/80 via-gray-800/80 to-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-2xl overflow-hidden">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <img
                    src={featuredPost.image}
                    alt={featuredPost.title}
                    className="w-full h-64 md:h-full object-cover"
                  />
                </div>
                <div className="md:w-1/2 p-8">
                  <div className="flex items-center mb-4">
                    <span className="bg-yellow-500/20 text-yellow-400 px-3 py-1 rounded-full text-sm font-medium">
                      Destacado
                    </span>
                    <span className="ml-3 text-yellow-400 text-sm">{featuredPost.category}</span>
                  </div>
                  <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white">
                    {featuredPost.title}
                  </h2>
                  <p className="text-gray-300 mb-6 leading-relaxed">
                    {featuredPost.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <img
                        src={featuredPost.author.avatar}
                        alt={featuredPost.author.name}
                        className="w-10 h-10 rounded-full mr-3"
                      />
                      <div>
                        <p className="text-white font-medium">{featuredPost.author.name}</p>
                        <p className="text-gray-400 text-sm">{featuredPost.author.role}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-gray-400 text-sm">{featuredPost.date}</p>
                      <p className="text-gray-500 text-xs">{featuredPost.readTime}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Search and Filter */}
          <div className="mb-12">
            <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto mb-8">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-3 border border-gray-700 rounded-lg bg-gray-900/50 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                placeholder="Buscar artículos..."
              />
            </form>

            {/* Categories */}
            <div className="flex flex-wrap justify-center gap-3">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200 border ${
                    selectedCategory === category
                      ? 'border-yellow-500 text-yellow-400 bg-yellow-500/10'
                      : 'border-gray-700 text-gray-300 hover:border-yellow-500 hover:text-yellow-400 hover:bg-yellow-500/10'
                  }`}
                >
                  {category}
                </button>
              ))}
            </div>
          </div>

          {/* Blog Posts Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {filteredPosts.map((post) => (
              <article key={post.id} className="bg-gradient-to-br from-gray-900/60 via-gray-800/60 to-gray-900/60 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden hover:border-yellow-500/50 transition-all duration-300 group">
                <img
                  src={post.image}
                  alt={post.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="p-6">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-yellow-400 text-sm font-medium">{post.category}</span>
                    <span className="text-gray-500 text-xs">{post.readTime}</span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-white group-hover:text-yellow-400 transition-colors duration-200">
                    {post.title}
                  </h3>
                  <p className="text-gray-300 mb-4 text-sm leading-relaxed">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <img
                        src={post.author.avatar}
                        alt={post.author.name}
                        className="w-8 h-8 rounded-full mr-2"
                      />
                      <div>
                        <p className="text-white text-sm font-medium">{post.author.name}</p>
                      </div>
                    </div>
                    <span className="text-xs text-gray-400">{post.date}</span>
                  </div>
                </div>
              </article>
            ))}
          </div>

          {/* Newsletter Subscription */}
          <div className="bg-gradient-to-r from-yellow-500/10 via-yellow-400/5 to-yellow-500/10 border border-yellow-500/20 rounded-2xl p-8 text-center">
            <h3 className="text-2xl font-bold mb-4 text-white">
              Mantente Informado
            </h3>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Suscríbete a nuestro boletín legal y recibe los últimos artículos, análisis y actualizaciones directamente en tu correo.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Tu correo electrónico"
                className="flex-1 px-4 py-3 rounded-lg bg-gray-900/50 border border-gray-700 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              />
              <button className="px-6 py-3 bg-gradient-to-r from-yellow-500 to-yellow-600 text-gray-900 font-medium rounded-lg hover:from-yellow-400 hover:to-yellow-500 transition-all duration-200 transform hover:scale-105">
                Suscribirse
              </button>
            </div>
          </div>
        </div>
      </div>
      
      <Footer />
    </div>
  );
}
