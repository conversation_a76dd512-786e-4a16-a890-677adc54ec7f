'use client';

import React, { useState, useEffect } from 'react';
import { setRequestLocale } from 'next-intl/server';
import { Navigation } from '@/components/layout/navigation';
import UnifiedBackground from '@/components/layout/unified-background';
import Footer from '@/components/layout/footer';
import { Calendar, Clock, Search } from 'lucide-react';
import { blogService, BlogPost } from '@/services/blog-service';

// Remove the local BlogPost interface since we're using the one from the service

interface PageProps {
  params: { locale: string };
}

export default function BlogLegalPage({ params: { locale } }: PageProps) {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [featuredPosts, setFeaturedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadBlogData();
  }, []);

  const loadBlogData = async () => {
    try {
      setLoading(true);
      const [allPosts, featured] = await Promise.all([
        blogService.getAllPosts(),
        blogService.getFeaturedPosts(1)
      ]);
      setPosts(allPosts);
      setFeaturedPosts(featured);
    } catch (error) {
      console.error('Error loading blog data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      setLoading(true);
      try {
        const searchResults = await blogService.searchPosts(searchTerm);
        setPosts(searchResults);
      } catch (error) {
        console.error('Error searching posts:', error);
      } finally {
        setLoading(false);
      }
    } else {
      loadBlogData();
    }
  };

  const featuredPost = featuredPosts[0] || {
    id: 1,
    title: "Nuevas Reformas Laborales en Puerto Rico: Lo que Debes Saber",
    excerpt: "Un análisis detallado de los cambios recientes en la legislación laboral y cómo afectan a empleadores y empleados en la isla.",
    date: "15 de Junio, 2025",
    readTime: "5 min de lectura",
    category: "Derecho Laboral",
    image: "/images/blog/featured-post.jpg",
    author: {
      name: "Dra. María González",
      role: "Especialista en Derecho Laboral",
      avatar: "/images/team/maria-gonzalez.jpg"
    }
  };

  const blogPosts: BlogPost[] = [
    {
      id: 2,
      title: "Guía Completa sobre el Proceso de Divorcio en Puerto Rico",
      excerpt: "Todo lo que necesitas saber sobre los requisitos, plazos y consideraciones legales para un divorcio en la isla.",
      date: "2 de Junio, 2025",
      readTime: "7 min de lectura",
      category: "Derecho de Familia",
      image: "/images/blog/divorcio.jpg",
      author: {
        name: "Lic. Carlos Rivera",
        role: "Abogado de Familia",
        avatar: "/images/team/carlos-rivera.jpg"
      }
    },
    {
      id: 3,
      title: "Protección de Datos Personales: Lo que las Empresas Deben Saber",
      excerpt: "Análisis de las obligaciones legales para la protección de datos personales en el ámbito empresarial.",
      date: "20 de Mayo, 2025",
      readTime: "6 min de lectura",
      category: "Derecho Corporativo",
      image: "/images/blog/proteccion-datos.jpg",
      author: {
        name: "Lic. Sofía Martínez",
        role: "Especialista en Derecho Digital",
        avatar: "/images/team/sofia-martinez.jpg"
      }
    },
    {
      id: 4,
      title: "¿Qué Hacer Ante un Despido Injustificado?",
      excerpt: "Conoce tus derechos laborales y los pasos a seguir si has sido despedido sin causa justificada.",
      date: "5 de Mayo, 2025",
      readTime: "4 min de lectura",
      category: "Derecho Laboral",
      image: "/images/blog/despido-injustificado.jpg",
      author: {
        name: "Dra. Laura Torres",
        role: "Abogada Laboralista",
        avatar: "/images/team/laura-torres.jpg"
      }
    },
    {
      id: 5,
      title: "Contratos de Arrendamiento: Puntos Clave a Considerar",
      excerpt: "Guía práctica sobre los aspectos más importantes a revisar antes de firmar un contrato de arrendamiento.",
      date: "22 de Abril, 2025",
      readTime: "5 min de lectura",
      category: "Derecho Inmobiliario",
      image: "/images/blog/contratos-arrendamiento.jpg",
      author: {
        name: "Lic. Javier Méndez",
        role: "Especialista en Derecho Inmobiliario",
        avatar: "/images/team/javier-mendez.jpg"
      }
    },
    {
      id: 6,
      title: "Actualización: Cambios en el Código Civil de Puerto Rico",
      excerpt: "Resumen de las modificaciones recientes al Código Civil y su impacto en diversos aspectos legales.",
      date: "10 de Abril, 2025",
      readTime: "8 min de lectura",
      category: "Actualidad Legal",
      image: "/images/blog/codigo-civil.jpg",
      author: {
        name: "Dr. Roberto Sánchez",
        role: "Catedrático de Derecho Civil",
        avatar: "/images/team/roberto-sanchez.jpg"
      }
    },
    {
      id: 7,
      title: "Guía para Emprendedores: Elegir la Estructura Legal Adecuada",
      excerpt: "Análisis comparativo de las diferentes estructuras legales para emprender en Puerto Rico.",
      date: "28 de Marzo, 2025",
      readTime: "6 min de lectura",
      category: "Derecho Corporativo",
      image: "/images/blog/emprendedores.jpg",
      author: {
        name: "Lic. Ana López",
        role: "Asesora de Empresas",
        avatar: "/images/team/ana-lopez.jpg"
      }
    }
  ];

  const categories = [
    { name: 'Todos', count: 12 },
    { name: 'Derecho Laboral', count: 3 },
    { name: 'Derecho de Familia', count: 2 },
    { name: 'Derecho Corporativo', count: 4 },
    { name: 'Derecho Inmobiliario', count: 2 },
    { name: 'Actualidad Legal', count: 1 }
  ];

  const popularTags = [
    'Reformas Legales', 'Derechos Laborales', 'Divorcio', 'Contratos', 
    'Emprendimiento', 'Protección de Datos', 'Derechos del Inquilino',
    'Derechos del Arrendador', 'Despido', 'Negociaciones Colectivas'
  ];

  return (
    <div className="min-h-screen relative">
      <UnifiedBackground />
      <Navigation />
      
      <main className="relative z-10 pt-24 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Blog <span className="text-yellow-400">Legal</span>
          </h1>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto">
            Mantente informado con nuestros artículos sobre leyes, derechos y consejos legales en Puerto Rico.
          </p>
        </div>

        {/* Search and Filter */}
        <div className="mb-12">
          <form onSubmit={handleSearch} className="relative max-w-2xl mx-auto mb-8">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="block w-full pl-10 pr-3 py-3 border border-gray-700 rounded-lg bg-gray-900/50 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Buscar artículos..."
            />
          </form>

          <div className="flex flex-wrap justify-center gap-3 mb-8">
            {categories.map((category, index) => (
              <button
                key={index}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 ${
                  index === 0 
                    ? 'bg-yellow-500 text-gray-900 hover:bg-yellow-600' 
                    : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
                }`}
              >
                {category.name} ({category.count})
              </button>
            ))}
          </div>
        </div>

        {/* Featured Post */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-white mb-6">Artículo Destacado</h2>
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-800 hover:border-yellow-400/30 transition-all duration-300">
            <div className="md:flex">
              <div className="md:flex-shrink-0 md:w-1/2 bg-gray-800 flex items-center justify-center">
                <div className="w-full h-64 md:h-full bg-gray-700 flex items-center justify-center text-gray-400">
                  Imagen del Artículo
                </div>
              </div>
              <div className="p-8 md:w-1/2">
                <div className="flex items-center text-sm text-yellow-400 mb-2">
                  <span className="bg-yellow-400/10 px-3 py-1 rounded-full">{featuredPost.category}</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-3">{featuredPost.title}</h3>
                <p className="text-gray-300 mb-6">{featuredPost.excerpt}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center text-gray-400 text-sm mr-3">
                      {featuredPost.author.name.charAt(0)}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">{featuredPost.author.name}</p>
                      <div className="flex items-center text-xs text-gray-400">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span className="mr-3">{featuredPost.date}</span>
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{featuredPost.readTime}</span>
                      </div>
                    </div>
                  </div>
                  <button className="text-yellow-400 hover:text-yellow-300 text-sm font-medium">
                    Leer más →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Blog Posts Grid */}
        {loading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto"></div>
            <p className="text-gray-400 mt-4">Cargando artículos...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {posts.map((post) => (
            <article key={post.id} className="bg-gray-900/50 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-800 hover:border-yellow-400/30 transition-all duration-300">
              <div className="h-48 bg-gray-800 flex items-center justify-center text-gray-400">
                Imagen del Artículo
              </div>
              <div className="p-6">
                <div className="flex items-center text-xs text-yellow-400 mb-3">
                  <span className="bg-yellow-400/10 px-2 py-1 rounded">{post.category}</span>
                  <span className="mx-2 text-gray-500">•</span>
                  <span className="text-gray-400 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {post.readTime}
                  </span>
                </div>
                <h3 className="text-xl font-bold text-white mb-3 line-clamp-2">{post.title}</h3>
                <p className="text-gray-300 text-sm mb-4 line-clamp-3">{post.excerpt}</p>
                <div className="flex items-center justify-between pt-4 border-t border-gray-800">
                  <div className="flex items-center">
                    <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center text-gray-300 text-xs mr-2">
                      {post.author.name.charAt(0)}
                    </div>
                    <span className="text-sm text-gray-300">{post.author.name}</span>
                  </div>
                  <span className="text-xs text-gray-400">
                    {post.date && typeof post.date.toDate === 'function'
                      ? post.date.toDate().toLocaleDateString('es-ES')
                      : 'Fecha no disponible'
                    }
                  </span>
                </div>
              </div>
            </article>
            ))}
          </div>
        )}

        {/* Popular Tags */}
        <div className="mb-12">
          <h2 className="text-xl font-bold text-white mb-4">Etiquetas Populares</h2>
          <div className="flex flex-wrap gap-2">
            {popularTags.map((tag, index) => (
              <a
                key={index}
                href="#"
                className="inline-block bg-gray-800 hover:bg-gray-700 text-gray-300 text-sm px-3 py-1.5 rounded-full transition-colors duration-200"
              >
                {tag}
              </a>
            ))}
          </div>
        </div>

        {/* Newsletter */}
        <div className="bg-gradient-to-r from-yellow-600/10 to-yellow-900/10 border border-yellow-400/20 rounded-xl p-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-2">Suscríbete a Nuestro Boletín</h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Recibe las últimas noticias legales, actualizaciones y consejos directamente en tu bandeja de entrada.
          </p>
          <div className="max-w-md mx-auto flex">
            <input
              type="email"
              placeholder="Tu correo electrónico"
              className="flex-grow bg-gray-800 border border-gray-700 text-white rounded-l-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            />
            <button className="bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-medium px-6 py-3 rounded-r-lg transition-colors duration-200">
              Suscribirse
            </button>
          </div>
          <p className="text-xs text-gray-400 mt-3">
            Respetamos tu privacidad. Nunca compartiremos tu información.
          </p>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}

export function generateStaticParams() {
  return [{ locale: 'es' }, { locale: 'en' }];
}
