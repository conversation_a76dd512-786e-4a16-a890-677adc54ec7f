import { setRequestLocale } from 'next-intl/server';
import { Navigation } from '@/components/layout/navigation';
import UnifiedBackground from '@/components/layout/unified-background';
import Footer from '@/components/layout/footer';
import { Calendar, Clock, Search } from 'lucide-react';

interface PageProps {
  params: { locale: string };
}

export default function BlogLegalPage({ params: { locale } }: PageProps) {
  setRequestLocale(locale);

  const featuredPost = {
    id: 1,
    title: "Nuevas Reformas Laborales en Puerto Rico: Lo que Debes Saber",
    excerpt: "Un análisis detallado de los cambios recientes en la legislación laboral y cómo afectan a empleadores y empleados en la isla.",
    date: "15 de Enero, 2024",
    readTime: "5 min de lectura",
    category: "Derecho Laboral",
    author: {
      name: "<PERSON><PERSON><PERSON> <PERSON>",
      role: "Especialista en Derecho Laboral"
    }
  };

  const blogPosts = [
    {
      id: 2,
      title: "Guía Completa sobre el Proceso de Divorcio en Puerto Rico",
      excerpt: "Todo lo que necesitas saber sobre los requisitos, plazos y consideraciones legales para un divorcio en la isla.",
      date: "12 de Enero, 2024",
      readTime: "7 min de lectura",
      category: "Derecho de Familia",
      author: {
        name: "Lcdo. Carlos Rivera",
        role: "Especialista en Derecho de Familia"
      }
    },
    {
      id: 3,
      title: "Cómo Proteger tu Negocio: Aspectos Legales Esenciales",
      excerpt: "Los fundamentos legales que todo empresario debe conocer para proteger su negocio en Puerto Rico.",
      date: "10 de Enero, 2024",
      readTime: "6 min de lectura",
      category: "Derecho Corporativo",
      author: {
        name: "Lcda. Ana Martínez",
        role: "Especialista en Derecho Corporativo"
      }
    }
  ];

  return (
    <div className="min-h-screen relative">
      <UnifiedBackground />
      <Navigation />

      <main className="relative z-10 pt-24 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Blog <span className="text-yellow-400">Legal</span>
          </h1>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto">
            Mantente informado con nuestros artículos sobre leyes, derechos y consejos legales en Puerto Rico.
          </p>
        </div>

        {/* Search */}
        <div className="mb-12">
          <div className="relative max-w-2xl mx-auto">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-3 border border-gray-700 rounded-lg bg-gray-900/50 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
              placeholder="Buscar artículos..."
            />
          </div>
        </div>

        {/* Featured Post */}
        <div className="mb-16">
          <h2 className="text-2xl font-bold text-white mb-6">Artículo Destacado</h2>
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-800 hover:border-yellow-400/30 transition-all duration-300">
            <div className="md:flex">
              <div className="md:flex-shrink-0 md:w-1/2 bg-gray-800 flex items-center justify-center">
                <div className="w-full h-64 md:h-full bg-gray-700 flex items-center justify-center text-gray-400">
                  Imagen del Artículo
                </div>
              </div>
              <div className="p-8 md:w-1/2">
                <div className="flex items-center text-sm text-yellow-400 mb-2">
                  <span className="bg-yellow-400/10 px-3 py-1 rounded-full">{featuredPost.category}</span>
                </div>
                <h3 className="text-2xl font-bold text-white mb-3">{featuredPost.title}</h3>
                <p className="text-gray-300 mb-6">{featuredPost.excerpt}</p>
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center text-gray-400 text-sm mr-3">
                      {featuredPost.author.name.charAt(0)}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-white">{featuredPost.author.name}</p>
                      <div className="flex items-center text-xs text-gray-400">
                        <Calendar className="h-3 w-3 mr-1" />
                        <span className="mr-3">{featuredPost.date}</span>
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{featuredPost.readTime}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Blog Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {blogPosts.map((post) => (
            <article key={post.id} className="bg-gray-900/50 backdrop-blur-sm rounded-xl overflow-hidden border border-gray-800 hover:border-yellow-400/30 transition-all duration-300">
              <div className="h-48 bg-gray-800 flex items-center justify-center text-gray-400">
                Imagen del Artículo
              </div>
              <div className="p-6">
                <div className="flex items-center text-xs text-yellow-400 mb-3">
                  <span className="bg-yellow-400/10 px-2 py-1 rounded">{post.category}</span>
                  <span className="mx-2 text-gray-500">•</span>
                  <span className="text-gray-400 flex items-center">
                    <Clock className="h-3 w-3 mr-1" />
                    {post.readTime}
                  </span>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">{post.title}</h3>
                <p className="text-gray-300 text-sm mb-4">{post.excerpt}</p>
                <div className="flex items-center justify-between pt-4 border-t border-gray-800">
                  <div className="flex items-center">
                    <div className="h-8 w-8 rounded-full bg-gray-700 flex items-center justify-center text-gray-300 text-xs mr-2">
                      {post.author.name.charAt(0)}
                    </div>
                    <span className="text-sm text-gray-300">{post.author.name}</span>
                  </div>
                  <span className="text-xs text-gray-400">{post.date}</span>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* Newsletter */}
        <div className="bg-gradient-to-r from-yellow-600/10 to-yellow-900/10 border border-yellow-400/20 rounded-xl p-8 text-center">
          <h2 className="text-2xl font-bold text-white mb-2">Suscríbete a Nuestro Boletín</h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Recibe las últimas noticias legales, actualizaciones y consejos directamente en tu bandeja de entrada.
          </p>
          <div className="max-w-md mx-auto flex">
            <input
              type="email"
              placeholder="Tu correo electrónico"
              className="flex-grow bg-gray-800 border border-gray-700 text-white rounded-l-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            />
            <button className="bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-medium px-6 py-3 rounded-r-lg transition-colors duration-200">
              Suscribirse
            </button>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}

export function generateStaticParams() {
  return [{ locale: 'es' }, { locale: 'en' }];
}
