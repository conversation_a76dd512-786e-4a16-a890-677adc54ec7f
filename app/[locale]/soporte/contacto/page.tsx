import { setRequestLocale } from 'next-intl/server';
import { Navigation } from '@/components/layout/navigation';
import UnifiedBackground from '@/components/layout/unified-background';
import Footer from '@/components/layout/footer';
import { MapPin, Phone, Mail, Clock, Send, CheckCircle } from 'lucide-react';

interface PageProps {
  params: { locale: string };
}

export default function ContactoPage({ params: { locale } }: PageProps) {
  setRequestLocale(locale);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const data = {
      name: formData.get('name'),
      email: formData.get('email'),
      subject: formData.get('subject'),
      message: formData.get('message'),
      privacy: formData.get('privacy')
    };

    // Here you would typically send to your backend
    console.log('Contact form submitted:', data);

    // Show success message
    alert('¡Gracias por tu mensaje! Nos pondremos en contacto contigo pronto.');
  };
  
  const contactInfo = [
    {
      icon: <MapPin className="h-6 w-6 text-yellow-400" />,
      title: "Oficina Principal",
      description: "123 Calle Principal, San Juan, PR 00901"
    },
    {
      icon: <Phone className="h-6 w-6 text-yellow-400" />,
      title: "Teléfono",
      description: "+****************"
    },
    {
      icon: <Mail className="h-6 w-6 text-yellow-400" />,
      title: "Correo Electrónico",
      description: "<EMAIL>"
    },
    {
      icon: <Clock className="h-6 w-6 text-yellow-400" />,
      title: "Horario de Atención",
      description: "Lunes a Viernes: 8:00 AM - 5:00 PM"
    }
  ];

  return (
    <div className="min-h-screen relative">
      <UnifiedBackground />
      <Navigation />
      
      <main className="relative z-10 pt-24 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Contáct<span className="text-yellow-400">anos</span>
          </h1>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto">
            Estamos aquí para ayudarte. Envíanos un mensaje y nos pondremos en contacto contigo lo antes posible.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-800">
            <h2 className="text-2xl font-bold text-white mb-6">Envíanos un Mensaje</h2>
            
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">Nombre Completo</label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    required
                    className="w-full bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                    placeholder="Tu nombre"
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">Correo Electrónico</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    required
                    className="w-full bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              
              <div>
                <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-1">Asunto</label>
                <input
                  type="text"
                  id="subject"
                  name="subject"
                  required
                  className="w-full bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  placeholder="¿Cómo podemos ayudarte?"
                />
              </div>
              
              <div>
                <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-1">Mensaje</label>
                <textarea
                  id="message"
                  name="message"
                  rows={5}
                  required
                  className="w-full bg-gray-800 border border-gray-700 text-white rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
                  placeholder="Describe tu consulta o inquietud..."
                ></textarea>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="privacy"
                  name="privacy"
                  required
                  className="h-4 w-4 text-yellow-500 border-gray-700 rounded focus:ring-yellow-500"
                />
                <label htmlFor="privacy" className="ml-2 text-sm text-gray-300">
                  Acepto la <a href="/politica-privacidad" className="text-yellow-400 hover:underline">Política de Privacidad</a>
                </label>
              </div>
              
              <button
                type="submit"
                className="w-full bg-yellow-500 hover:bg-yellow-600 text-gray-900 font-medium py-3 px-6 rounded-lg transition-colors duration-200 flex items-center justify-center gap-2"
              >
                <Send className="h-5 w-5" />
                Enviar Mensaje
              </button>
              
              <div className="bg-green-500/10 border border-green-500/20 text-green-400 text-sm p-4 rounded-lg flex items-start gap-3">
                <CheckCircle className="h-5 w-5 mt-0.5 flex-shrink-0" />
                <p>¡Gracias por tu mensaje! Nos pondremos en contacto contigo en un plazo de 24-48 horas.</p>
              </div>
            </form>
          </div>
          
          {/* Contact Information */}
          <div>
            <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-800 mb-8">
              <h2 className="text-2xl font-bold text-white mb-6">Información de Contacto</h2>
              
              <div className="space-y-6">
                {contactInfo.map((item, index) => (
                  <div key={index} className="flex items-start">
                    <div className="bg-gray-800 p-2 rounded-lg">
                      {item.icon}
                    </div>
                    <div className="ml-4">
                      <h3 className="text-lg font-semibold text-yellow-400">{item.title}</h3>
                      <p className="text-gray-300">{item.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-8 border border-gray-800">
              <h2 className="text-2xl font-bold text-white mb-6">Síguenos</h2>
              <div className="flex space-x-4">
                {['Facebook', 'Twitter', 'LinkedIn', 'Instagram'].map((social) => (
                  <a
                    key={social}
                    href="#"
                    className="bg-gray-800 hover:bg-gray-700 text-white p-3 rounded-lg transition-colors duration-200"
                    aria-label={social}
                  >
                    <span className="sr-only">{social}</span>
                    <div className="h-5 w-5" />
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        {/* Map */}
        <div className="mt-16 rounded-xl overflow-hidden border border-gray-800">
          <div className="h-96 w-full bg-gray-800 flex items-center justify-center">
            <p className="text-gray-400">Mapa de ubicación</p>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}

export function generateStaticParams() {
  return [{ locale: 'es' }, { locale: 'en' }];
}
