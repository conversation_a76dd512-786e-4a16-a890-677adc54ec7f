import { setRequestLocale } from 'next-intl/server';
import { Navigation } from '@/components/layout/navigation';
import UnifiedBackground from '@/components/layout/unified-background';
import Footer from '@/components/layout/footer';
import { HelpCircle, MessageSquare, Phone, Mail } from 'lucide-react';

interface PageProps {
  params: { locale: string };
}

export default function CentroAyudaPage({ params: { locale } }: PageProps) {
  setRequestLocale(locale);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const query = formData.get('search') as string;
    if (query) {
      // Redirect to FAQ page with search query
      window.location.href = `/es/soporte/preguntas-frecuentes?q=${encodeURIComponent(query)}`;
    }
  };
  
  const helpTopics = [
    {
      title: "Cómo Crear una Cuenta",
      description: "Guía paso a paso para registrarse en nuestra plataforma y comenzar a buscar abogados.",
      icon: <HelpCircle className="h-6 w-6 text-yellow-400" />
    },
    {
      title: "Búsqueda de Abogados",
      description: "Aprenda a encontrar el abogado perfecto para sus necesidades legales.",
      icon: <HelpCircle className="h-6 w-6 text-yellow-400" />
    },
    {
      title: "Proceso de Pago",
      description: "Todo lo que necesita saber sobre nuestros métodos de pago y facturación.",
      icon: <HelpCircle className="h-6 w-6 text-yellow-400" />
    },
    {
      title: "Seguridad y Privacidad",
      description: "Cómo protegemos su información personal y sus datos legales.",
      icon: <HelpCircle className="h-6 w-6 text-yellow-400" />
    }
  ];

  const contactMethods = [
    {
      title: "Chat en Vivo",
      description: "Hable con nuestro equipo de soporte",
      icon: <MessageSquare className="h-6 w-6 text-yellow-400" />,
      action: "Iniciar Chat",
      onClick: () => {
        // Open chat widget or redirect to chat
        window.open('https://tawk.to/chat/delawpr', '_blank');
      }
    },
    {
      title: "Llámenos",
      description: "+****************",
      icon: <Phone className="h-6 w-6 text-yellow-400" />,
      action: "Llamar Ahora",
      onClick: () => {
        window.open('tel:+17875550123', '_self');
      }
    },
    {
      title: "Correo Electrónico",
      description: "<EMAIL>",
      icon: <Mail className="h-6 w-6 text-yellow-400" />,
      action: "Enviar Email",
      onClick: () => {
        window.open('mailto:<EMAIL>?subject=Consulta de Soporte', '_self');
      }
    }
  ];

  return (
    <div className="min-h-screen relative">
      <UnifiedBackground />
      <Navigation />
      
      <main className="relative z-10 pt-24 pb-16 px-4 sm:px-6 lg:px-8 max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Centro de <span className="text-yellow-400">Ayuda</span>
          </h1>
          <p className="text-gray-300 text-lg max-w-3xl mx-auto">
            Encuentre respuestas a sus preguntas o póngase en contacto con nuestro equipo de soporte.
          </p>
        </div>

        <div className="mb-16">
          <form onSubmit={handleSearch} className="relative">
            <input
              type="text"
              name="search"
              placeholder="¿En qué podemos ayudarle?"
              className="w-full bg-gray-900/50 border border-gray-800 text-white rounded-lg py-4 px-6 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-yellow-500 text-gray-900 px-6 py-2 rounded-lg font-medium hover:bg-yellow-400 transition-colors duration-200"
            >
              Buscar
            </button>
          </form>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {helpTopics.map((topic, index) => (
            <div 
              key={index}
              className="bg-gray-900/50 backdrop-blur-sm rounded-xl p-6 border border-gray-800 hover:border-yellow-400/30 transition-all duration-300"
            >
              <div className="flex items-start">
                <div className="flex-shrink-0 bg-gray-800 p-2 rounded-lg">
                  {topic.icon}
                </div>
                <div className="ml-4">
                  <h3 className="text-xl font-semibold text-yellow-400 mb-2">{topic.title}</h3>
                  <p className="text-gray-300 mb-4">{topic.description}</p>
                  <button className="text-yellow-400 hover:text-yellow-300 text-sm font-medium">
                    Leer más →
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-yellow-600/10 to-yellow-900/10 border border-yellow-400/20 rounded-xl p-8 mb-16">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">¿No encontró lo que buscaba?</h2>
          <p className="text-gray-300 text-center mb-8 max-w-2xl mx-auto">
            Nuestro equipo de soporte está disponible para ayudarle con cualquier pregunta o inquietud que pueda tener.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-10">
            {contactMethods.map((method, index) => (
              <div 
                key={index}
                className="bg-gray-900/50 rounded-lg p-6 border border-gray-800 text-center"
              >
                <div className="flex justify-center mb-4">
                  <div className="bg-gray-800 p-3 rounded-full">
                    {method.icon}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-yellow-400 mb-2">{method.title}</h3>
                <p className="text-gray-300 text-sm mb-4">{method.description}</p>
                <button
                  onClick={method.onClick}
                  className="text-yellow-400 hover:text-yellow-300 text-sm font-medium transition-colors duration-200"
                >
                  {method.action}
                </button>
              </div>
            ))}
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}

export function generateStaticParams() {
  return [{ locale: 'es' }, { locale: 'en' }];
}
