import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  limit, 
  where,
  Timestamp 
} from 'firebase/firestore';
import { db } from '@/lib/firebase';

export interface BlogPost {
  id: string;
  title: string;
  excerpt: string;
  content: string;
  date: Timestamp;
  readTime: string;
  category: string;
  image: string;
  featured: boolean;
  published: boolean;
  author: {
    name: string;
    role: string;
    avatar: string;
    id: string;
  };
  tags: string[];
  views: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface BlogCategory {
  id: string;
  name: string;
  description: string;
  color: string;
}

class BlogService {
  private readonly COLLECTION_NAME = 'blog_posts';
  private readonly CATEGORIES_COLLECTION = 'blog_categories';

  // Get all published blog posts
  async getAllPosts(): Promise<BlogPost[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('published', '==', true),
        orderBy('date', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogPost));
    } catch (error) {
      console.error('Error fetching blog posts:', error);
      return this.getMockPosts(); // Fallback to mock data
    }
  }

  // Get featured posts
  async getFeaturedPosts(limitCount: number = 3): Promise<BlogPost[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('published', '==', true),
        where('featured', '==', true),
        orderBy('date', 'desc'),
        limit(limitCount)
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogPost));
    } catch (error) {
      console.error('Error fetching featured posts:', error);
      return this.getMockPosts().filter(post => post.featured).slice(0, limitCount);
    }
  }

  // Get posts by category
  async getPostsByCategory(category: string): Promise<BlogPost[]> {
    try {
      const q = query(
        collection(db, this.COLLECTION_NAME),
        where('published', '==', true),
        where('category', '==', category),
        orderBy('date', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogPost));
    } catch (error) {
      console.error('Error fetching posts by category:', error);
      return this.getMockPosts().filter(post => post.category === category);
    }
  }

  // Get single post by ID
  async getPostById(id: string): Promise<BlogPost | null> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        return {
          id: docSnap.id,
          ...docSnap.data()
        } as BlogPost;
      }
      return null;
    } catch (error) {
      console.error('Error fetching post by ID:', error);
      return null;
    }
  }

  // Search posts
  async searchPosts(searchTerm: string): Promise<BlogPost[]> {
    try {
      // Note: Firestore doesn't support full-text search natively
      // This is a simple implementation - for production, consider using Algolia or similar
      const allPosts = await this.getAllPosts();
      return allPosts.filter(post => 
        post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.content.toLowerCase().includes(searchTerm.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    } catch (error) {
      console.error('Error searching posts:', error);
      return [];
    }
  }

  // Get blog categories
  async getCategories(): Promise<BlogCategory[]> {
    try {
      const querySnapshot = await getDocs(collection(db, this.CATEGORIES_COLLECTION));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      } as BlogCategory));
    } catch (error) {
      console.error('Error fetching categories:', error);
      return this.getMockCategories();
    }
  }

  // Create new post (admin only)
  async createPost(postData: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt' | 'views'>): Promise<string> {
    try {
      const now = Timestamp.now();
      const docRef = await addDoc(collection(db, this.COLLECTION_NAME), {
        ...postData,
        views: 0,
        createdAt: now,
        updatedAt: now
      });
      return docRef.id;
    } catch (error) {
      console.error('Error creating post:', error);
      throw error;
    }
  }

  // Update post (admin only)
  async updatePost(id: string, postData: Partial<BlogPost>): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      await updateDoc(docRef, {
        ...postData,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating post:', error);
      throw error;
    }
  }

  // Delete post (admin only)
  async deletePost(id: string): Promise<void> {
    try {
      await deleteDoc(doc(db, this.COLLECTION_NAME, id));
    } catch (error) {
      console.error('Error deleting post:', error);
      throw error;
    }
  }

  // Increment post views
  async incrementViews(id: string): Promise<void> {
    try {
      const docRef = doc(db, this.COLLECTION_NAME, id);
      const docSnap = await getDoc(docRef);
      
      if (docSnap.exists()) {
        const currentViews = docSnap.data().views || 0;
        await updateDoc(docRef, {
          views: currentViews + 1
        });
      }
    } catch (error) {
      console.error('Error incrementing views:', error);
    }
  }

  // Mock data for fallback
  private getMockPosts(): BlogPost[] {
    const now = Timestamp.now();
    return [
      {
        id: '1',
        title: 'Nuevas Reformas Laborales en Puerto Rico: Lo que Debes Saber',
        excerpt: 'Un análisis detallado de los cambios recientes en la legislación laboral y cómo afectan a empleadores y empleados en la isla.',
        content: 'Contenido completo del artículo sobre reformas laborales...',
        date: now,
        readTime: '5 min de lectura',
        category: 'Derecho Laboral',
        image: '/images/blog/featured-post.jpg',
        featured: true,
        published: true,
        author: {
          id: 'author1',
          name: 'Dra. María González',
          role: 'Especialista en Derecho Laboral',
          avatar: '/images/team/maria-gonzalez.jpg'
        },
        tags: ['derecho laboral', 'reformas', 'puerto rico'],
        views: 245,
        createdAt: now,
        updatedAt: now
      },
      {
        id: '2',
        title: 'Guía Completa sobre el Proceso de Divorcio en Puerto Rico',
        excerpt: 'Todo lo que necesitas saber sobre los requisitos, plazos y consideraciones legales para un divorcio en la isla.',
        content: 'Contenido completo del artículo sobre divorcio...',
        date: now,
        readTime: '7 min de lectura',
        category: 'Derecho de Familia',
        image: '/images/blog/divorce-guide.jpg',
        featured: false,
        published: true,
        author: {
          id: 'author2',
          name: 'Lcdo. Carlos Rivera',
          role: 'Especialista en Derecho de Familia',
          avatar: '/images/team/carlos-rivera.jpg'
        },
        tags: ['divorcio', 'familia', 'proceso legal'],
        views: 189,
        createdAt: now,
        updatedAt: now
      }
    ];
  }

  private getMockCategories(): BlogCategory[] {
    return [
      { id: '1', name: 'Derecho Laboral', description: 'Temas relacionados con empleo y trabajo', color: 'blue' },
      { id: '2', name: 'Derecho de Familia', description: 'Asuntos familiares y matrimoniales', color: 'pink' },
      { id: '3', name: 'Derecho Corporativo', description: 'Negocios y empresas', color: 'green' },
      { id: '4', name: 'Derecho Penal', description: 'Defensa criminal', color: 'red' },
      { id: '5', name: 'Derecho Inmobiliario', description: 'Propiedades y bienes raíces', color: 'yellow' }
    ];
  }
}

export const blogService = new BlogService();
